# 我的第一个Streamlit项目

这是一个使用Streamlit构建的Python Web应用项目。

## 项目结构

```
firstPyProject/
├── venv/                 # Python虚拟环境
├── app.py               # 主应用文件
├── requirements.txt     # 项目依赖
└── README.md           # 项目说明
```

## 环境设置

### 1. 激活虚拟环境

```bash
source venv/bin/activate
```

### 2. 安装依赖（如果需要）

```bash
pip install -r requirements.txt
```

## 运行应用

在激活虚拟环境后，运行以下命令启动Streamlit应用：

```bash
streamlit run app.py
```

应用将在浏览器中自动打开，通常地址为：`http://localhost:8501`

## 功能特性

- 📊 数据展示和编辑
- 📈 多种图表类型（线图、柱状图、面积图）
- 🎛️ 交互式组件（输入框、滑块、选择框）
- 📱 响应式布局

## 开发提示

- 修改代码后保存文件，Streamlit会自动重新加载应用
- 使用侧边栏导航不同的页面
- 可以在`app.py`中添加更多功能和页面

## 退出虚拟环境

完成开发后，可以使用以下命令退出虚拟环境：

```bash
deactivate
```
