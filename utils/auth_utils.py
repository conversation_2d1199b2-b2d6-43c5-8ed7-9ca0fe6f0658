"""
认证工具函数
用于在多页应用中处理认证相关的功能
"""

import streamlit as st
import yaml
from yaml.loader import SafeLoader

def check_authentication():
    """检查用户是否已认证"""
    return st.session_state.get('authentication_status') == True

def require_authentication():
    """装饰器：要求用户必须登录才能访问页面"""
    if not check_authentication():
        st.error("⚠️ 请先登录以访问此页面")
        st.stop()

def get_current_user():
    """获取当前登录用户的信息"""
    if check_authentication():
        return {
            'username': st.session_state.get('username'),
            'name': st.session_state.get('name'),
            'email': st.session_state.get('email', ''),
            'roles': st.session_state.get('roles', [])
        }
    return None

def save_config(config):
    """保存配置文件"""
    with open('./config.yaml', 'w') as file:
        yaml.dump(config, file, default_flow_style=False, allow_unicode=True)

def load_config():
    """加载配置文件"""
    with open('./config.yaml') as file:
        return yaml.load(file, Loader=SafeLoader)

def has_role(role):
    """检查当前用户是否具有指定角色"""
    user = get_current_user()
    if user and 'roles' in user:
        return role in user['roles']
    return False

def display_user_info():
    """在侧边栏显示用户信息"""
    if check_authentication():
        user = get_current_user()
        with st.sidebar:
            st.success(f"👋 欢迎，{user['name']}")
            st.caption(f"用户名: {user['username']}")
            if user['roles']:
                st.caption(f"角色: {', '.join(user['roles'])}")
    else:
        with st.sidebar:
            st.warning("🔒 未登录")
            st.info("请登录以访问所有功能")

def render_hidden_login():
    """在需要鉴权的页面渲染隐藏的登录组件"""
    if 'authenticator' in st.session_state:
        authenticator = st.session_state.authenticator
        try:
            # 使用 'unrendered' 位置来隐藏登录组件，但保持功能
            authenticator.login(location='unrendered')
        except Exception as e:
            st.error(f"认证错误: {e}")
