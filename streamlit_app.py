import streamlit as st
import streamlit_authenticator as stauth
import yaml
from yaml.loader import SafeLoader
import os

# 设置页面配置
st.set_page_config(
    page_title="个人工具网站",
    page_icon="🏠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化认证器
@st.cache_resource
def init_authenticator():
    """初始化认证器"""
    with open('./config.yaml') as file:
        config = yaml.load(file, Loader=SafeLoader)
    
    authenticator = stauth.Authenticate(
        config['credentials'],
        config['cookie']['name'],
        config['cookie']['key'],
        config['cookie']['expiry_days']
    )
    return authenticator, config

# 保存配置文件
def save_config(config):
    """保存配置文件"""
    with open('./config.yaml', 'w') as file:
        yaml.dump(config, file, default_flow_style=False, allow_unicode=True)

# 获取认证器和配置
authenticator, config = init_authenticator()

# 将认证器保存到session state中，供其他页面使用
if 'authenticator' not in st.session_state:
    st.session_state.authenticator = authenticator
if 'config' not in st.session_state:
    st.session_state.config = config

# 定义页面
def login_page():
    """登录页面函数"""
    st.title("🔐 登录")
    st.markdown("---")
    
    try:
        authenticator.login(location='main')
    except Exception as e:
        st.error(f"登录错误: {e}")
    
    # 保存配置
    save_config(config)
    
    if st.session_state.get('authentication_status') is False:
        st.error('用户名或密码错误')
    elif st.session_state.get('authentication_status') is None:
        st.warning('请输入用户名和密码')
        
        # 提供注册选项
        st.markdown("---")
        st.info("还没有账户？请联系管理员注册。")

def home_page():
    """主页函数"""
    st.title("🏠 欢迎来到个人工具网站")
    st.markdown("---")
    
    # 显示欢迎信息
    if st.session_state.get('authentication_status'):
        st.success(f"欢迎回来，{st.session_state.get('name')}！")
        
        # 显示功能概览
        st.markdown("## 🛠️ 可用工具")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("""
            ### 📊 数据工具
            - 计算器
            - 数据分析
            - 图表生成
            """)
        
        with col2:
            st.markdown("""
            ### 📝 文本工具  
            - 文本处理
            - 格式转换
            - 编码解码
            """)
        
        with col3:
            st.markdown("""
            ### 📁 文件工具
            - 文件管理
            - 格式转换
            - 批量处理
            """)
            
        st.markdown("---")
        st.info("请使用左侧导航菜单访问各种工具。")
    else:
        st.warning("请先登录以访问所有功能。")

def calculator_page():
    """计算器页面函数"""
    st.title("🧮 计算器工具")
    st.markdown("---")
    
    # 简单计算器
    st.subheader("基础计算器")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        num1 = st.number_input("第一个数字", value=0.0)
    
    with col2:
        operation = st.selectbox("运算符", ["+", "-", "*", "/", "**"])
    
    with col3:
        num2 = st.number_input("第二个数字", value=0.0)
    
    if st.button("计算"):
        try:
            if operation == "+":
                result = num1 + num2
            elif operation == "-":
                result = num1 - num2
            elif operation == "*":
                result = num1 * num2
            elif operation == "/":
                if num2 != 0:
                    result = num1 / num2
                else:
                    st.error("除数不能为零！")
                    return
            elif operation == "**":
                result = num1 ** num2
            
            st.success(f"结果: {num1} {operation} {num2} = {result}")
        except Exception as e:
            st.error(f"计算错误: {e}")

def text_tools_page():
    """文本工具页面函数"""
    st.title("📝 文本处理工具")
    st.markdown("---")

    # 文本处理工具
    text_input = st.text_area("输入文本", height=200)

    if text_input:
        col1, col2 = st.columns(2)

        with col1:
            st.subheader("文本统计")
            st.write(f"字符数: {len(text_input)}")
            st.write(f"单词数: {len(text_input.split())}")
            st.write(f"行数: {len(text_input.splitlines())}")

        with col2:
            st.subheader("文本转换")
            if st.button("转为大写"):
                st.text_area("大写结果", text_input.upper(), height=100)
            if st.button("转为小写"):
                st.text_area("小写结果", text_input.lower(), height=100)
            if st.button("首字母大写"):
                st.text_area("首字母大写结果", text_input.title(), height=100)

def profile_page():
    """个人资料页面函数"""
    st.title("👤 个人资料")
    st.markdown("---")

    if st.session_state.get('authentication_status'):
        # 显示用户信息
        st.subheader("用户信息")
        st.write(f"**用户名:** {st.session_state.get('username')}")
        st.write(f"**姓名:** {st.session_state.get('name')}")

        # 修改密码
        st.markdown("---")
        st.subheader("修改密码")
        try:
            if authenticator.reset_password(st.session_state.get('username')):
                st.success('密码修改成功！')
                save_config(config)
        except Exception as e:
            st.error(f"密码修改错误: {e}")

        # 更新用户详情
        st.markdown("---")
        st.subheader("更新个人信息")
        try:
            if authenticator.update_user_details(st.session_state.get('username')):
                st.success('个人信息更新成功！')
                save_config(config)
        except Exception as e:
            st.error(f"信息更新错误: {e}")
    else:
        st.error("请先登录以访问个人资料。")

def logout_page():
    """登出页面函数"""
    st.title("👋 登出")
    st.markdown("---")

    if st.session_state.get('authentication_status'):
        st.write(f"再见，{st.session_state.get('name')}！")
        authenticator.logout(location='main')
        st.success("您已成功登出。")
    else:
        st.info("您尚未登录。")

# 检查认证状态的辅助函数
def check_authentication():
    """检查用户是否已认证"""
    return st.session_state.get('authentication_status') == True

# 高级工具页面函数
def advanced_calculator_page():
    """高级计算器页面"""
    from utils.auth_utils import require_authentication, render_hidden_login, display_user_info
    import math
    import numpy as np
    import matplotlib.pyplot as plt

    # 渲染隐藏的登录组件
    render_hidden_login()

    # 检查认证
    require_authentication()

    # 显示用户信息
    display_user_info()

    st.title("🔬 高级计算器")
    st.markdown("---")

    # 创建标签页
    tab1, tab2, tab3 = st.tabs(["科学计算", "统计计算", "图形计算"])

    with tab1:
        st.subheader("科学计算")

        # 三角函数
        st.markdown("#### 三角函数")
        angle = st.number_input("角度 (度)", value=0.0, key="trig_angle")
        angle_rad = math.radians(angle)

        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("sin", key="sin_btn"):
                result = math.sin(angle_rad)
                st.write(f"sin({angle}°) = {result:.6f}")

        with col2:
            if st.button("cos", key="cos_btn"):
                result = math.cos(angle_rad)
                st.write(f"cos({angle}°) = {result:.6f}")

        with col3:
            if st.button("tan", key="tan_btn"):
                result = math.tan(angle_rad)
                st.write(f"tan({angle}°) = {result:.6f}")

        # 对数和指数
        st.markdown("#### 对数和指数")
        log_num = st.number_input("数字", value=1.0, min_value=0.001, key="log_num")

        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("ln", key="ln_btn"):
                result = math.log(log_num)
                st.write(f"ln({log_num}) = {result:.6f}")

        with col2:
            if st.button("log10", key="log10_btn"):
                result = math.log10(log_num)
                st.write(f"log₁₀({log_num}) = {result:.6f}")

        with col3:
            if st.button("e^x", key="exp_btn"):
                result = math.exp(log_num)
                st.write(f"e^{log_num} = {result:.6f}")

    with tab2:
        st.subheader("统计计算")

        # 数据输入
        data_input = st.text_area("输入数据 (用逗号分隔)", "1,2,3,4,5", key="stats_data")

        try:
            data = [float(x.strip()) for x in data_input.split(',') if x.strip()]

            if data:
                # 计算统计量
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("#### 基本统计")
                    st.write(f"数据个数: {len(data)}")
                    st.write(f"最小值: {min(data):.4f}")
                    st.write(f"最大值: {max(data):.4f}")
                    st.write(f"平均值: {np.mean(data):.4f}")
                    st.write(f"中位数: {np.median(data):.4f}")

                with col2:
                    st.markdown("#### 高级统计")
                    st.write(f"标准差: {np.std(data):.4f}")
                    st.write(f"方差: {np.var(data):.4f}")
                    st.write(f"总和: {sum(data):.4f}")
                    st.write(f"范围: {max(data) - min(data):.4f}")

                # 显示数据分布图
                st.markdown("#### 数据分布")
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 4))

                # 直方图
                ax1.hist(data, bins=min(10, len(data)), alpha=0.7, color='skyblue')
                ax1.set_title('数据分布直方图')
                ax1.set_xlabel('值')
                ax1.set_ylabel('频次')

                # 箱线图
                ax2.boxplot(data)
                ax2.set_title('箱线图')
                ax2.set_ylabel('值')

                st.pyplot(fig)

        except ValueError:
            st.error("请输入有效的数字，用逗号分隔")

    with tab3:
        st.subheader("函数图形")

        # 函数选择
        func_type = st.selectbox("选择函数类型",
                                ["线性函数", "二次函数", "三角函数"],
                                key="func_type")

        # 参数设置
        x_min = st.number_input("x 最小值", value=-10.0, key="x_min")
        x_max = st.number_input("x 最大值", value=10.0, key="x_max")

        if x_min >= x_max:
            st.error("x 最小值必须小于最大值")
        else:
            x = np.linspace(x_min, x_max, 1000)

            try:
                if func_type == "线性函数":
                    a = st.number_input("斜率 a", value=1.0, key="linear_a")
                    b = st.number_input("截距 b", value=0.0, key="linear_b")
                    y = a * x + b
                    title = f"y = {a}x + {b}"

                elif func_type == "二次函数":
                    a = st.number_input("a", value=1.0, key="quad_a")
                    b = st.number_input("b", value=0.0, key="quad_b")
                    c = st.number_input("c", value=0.0, key="quad_c")
                    y = a * x**2 + b * x + c
                    title = f"y = {a}x² + {b}x + {c}"

                elif func_type == "三角函数":
                    trig_func = st.selectbox("三角函数", ["sin", "cos", "tan"], key="trig_func")
                    amplitude = st.number_input("振幅", value=1.0, key="amplitude")
                    frequency = st.number_input("频率", value=1.0, key="frequency")

                    if trig_func == "sin":
                        y = amplitude * np.sin(frequency * x)
                    elif trig_func == "cos":
                        y = amplitude * np.cos(frequency * x)
                    else:  # tan
                        y = amplitude * np.tan(frequency * x)
                        # 限制tan函数的显示范围
                        y = np.where(np.abs(y) > 10, np.nan, y)

                    title = f"y = {amplitude}{trig_func}({frequency}x)"

                # 绘制图形
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.plot(x, y, 'b-', linewidth=2)
                ax.grid(True, alpha=0.3)
                ax.set_xlabel('x')
                ax.set_ylabel('y')
                ax.set_title(title)
                ax.axhline(y=0, color='k', linewidth=0.5)
                ax.axvline(x=0, color='k', linewidth=0.5)

                st.pyplot(fig)

            except Exception as e:
                st.error(f"绘图错误: {e}")

    st.markdown("---")
    st.info("💡 提示：这个高级计算器提供了科学计算、统计分析和函数图形绘制功能。")

# 定义页面配置
def get_pages():
    """根据认证状态返回可用页面"""
    if check_authentication():
        # 已登录用户可以访问所有页面
        return {
            "主页": [
                st.Page(home_page, title="🏠 主页", icon="🏠", default=True)
            ],
            "工具": [
                st.Page(calculator_page, title="🧮 基础计算器", icon="🧮"),
                st.Page(load_external_page("pages/tools/advanced_calculator.py"),
                       title="🔬 高级计算器", icon="🔬"),
                st.Page(text_tools_page, title="📝 基础文本工具", icon="📝"),
                st.Page(load_external_page("pages/tools/text_processor.py"),
                       title="📄 文本处理器", icon="📄"),
                st.Page(load_external_page("pages/tools/file_manager.py"),
                       title="📁 文件管理", icon="📁")
            ],
            "账户": [
                st.Page(profile_page, title="👤 个人资料", icon="👤"),
                st.Page(logout_page, title="👋 登出", icon="👋")
            ]
        }
    else:
        # 未登录用户只能访问登录页面
        return [
            st.Page(login_page, title="🔐 登录", icon="🔐", default=True)
        ]

# 主应用逻辑
def main():
    """主应用函数"""
    # 在侧边栏显示认证状态
    with st.sidebar:
        if check_authentication():
            st.success(f"已登录: {st.session_state.get('name')}")
        else:
            st.warning("未登录")

    # 获取页面配置
    pages = get_pages()

    # 创建导航
    pg = st.navigation(pages)

    # 运行选中的页面
    pg.run()

# 运行应用
if __name__ == "__main__":
    main()
